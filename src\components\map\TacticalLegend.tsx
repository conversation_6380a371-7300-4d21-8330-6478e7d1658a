import React, { useState, useCallback } from 'react';
import { Incident, Response, IncidentType } from '@/types/incident';
import {
  Eye,
  EyeOff,
  Edit3,
  Save,
  X,
  Plus,
  Trash2,
  ChevronDown,
  ChevronUp,
  Settings
} from 'lucide-react';
import Button from '@/components/ui/Button';

interface TacticalLegendProps {
  position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  incidents: Incident[];
  responses: Response[];
}

interface LegendItem {
  id: string;
  type: 'incident' | 'response';
  label: string;
  color: string;
  symbol: string;
  visible: boolean;
  count: number;
}

interface CustomSymbol {
  id: string;
  name: string;
  symbol: string;
  color: string;
  category: string;
}

const TacticalLegend: React.FC<TacticalLegendProps> = ({
  position,
  incidents,
  responses
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [showCustomSymbols, setShowCustomSymbols] = useState(false);
  
  // Custom symbols state
  const [customSymbols, setCustomSymbols] = useState<CustomSymbol[]>([
    { id: '1', name: 'Command Post', symbol: '⌘', color: '#FFD700', category: 'Command' },
    { id: '2', name: 'Observation Post', symbol: '👁', color: '#00FF00', category: 'Intelligence' },
    { id: '3', name: 'Supply Depot', symbol: '📦', color: '#8B4513', category: 'Logistics' },
    { id: '4', name: 'Medical Station', symbol: '🏥', color: '#FF0000', category: 'Medical' }
  ]);
  
  // Visibility state for legend items
  const [itemVisibility, setItemVisibility] = useState<Record<string, boolean>>({});
  
  // Get incident type colors
  const getIncidentColor = (type: IncidentType): string => {
    const colors = {
      'physical_raid': '#FF4444',
      'cyber_attack': '#4444FF',
      'intelligence_gathering': '#44FF44',
      'sabotage': '#FF8844',
      'assassination': '#FF44FF',
      'kidnapping': '#FFFF44',
      'bombing': '#FF0000',
      'other': '#888888'
    };
    return colors[type] || colors.other;
  };
  
  // Get incident type symbols
  const getIncidentSymbol = (type: IncidentType): string => {
    const symbols = {
      'physical_raid': '⚔️',
      'cyber_attack': '💻',
      'intelligence_gathering': '🔍',
      'sabotage': '💥',
      'assassination': '🎯',
      'kidnapping': '👤',
      'bombing': '💣',
      'other': '❓'
    };
    return symbols[type] || symbols.other;
  };
  
  // Generate legend items from current data
  const generateLegendItems = useCallback((): LegendItem[] => {
    const items: LegendItem[] = [];
    
    // Group incidents by type
    const incidentGroups = incidents.reduce((acc, incident) => {
      acc[incident.type] = (acc[incident.type] || 0) + 1;
      return acc;
    }, {} as Record<IncidentType, number>);
    
    // Add incident legend items
    Object.entries(incidentGroups).forEach(([type, count]) => {
      const incidentType = type as IncidentType;
      items.push({
        id: `incident-${type}`,
        type: 'incident',
        label: type.replace('_', ' ').toUpperCase(),
        color: getIncidentColor(incidentType),
        symbol: getIncidentSymbol(incidentType),
        visible: itemVisibility[`incident-${type}`] !== false,
        count
      });
    });
    
    // Add response legend item
    if (responses.length > 0) {
      items.push({
        id: 'response-all',
        type: 'response',
        label: 'RESPONSE OPERATIONS',
        color: '#00AA00',
        symbol: '🚁',
        visible: itemVisibility['response-all'] !== false,
        count: responses.length
      });
    }
    
    return items;
  }, [incidents, responses, itemVisibility]);
  
  const legendItems = generateLegendItems();
  
  // Toggle item visibility
  const toggleItemVisibility = (itemId: string) => {
    setItemVisibility(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }));
  };
  
  // Handle editing
  const startEditing = (itemId: string) => {
    setEditingItem(itemId);
  };
  
  const stopEditing = () => {
    setEditingItem(null);
  };
  
  // Add custom symbol
  const addCustomSymbol = () => {
    const newSymbol: CustomSymbol = {
      id: Date.now().toString(),
      name: 'New Symbol',
      symbol: '⭐',
      color: '#FFFFFF',
      category: 'Custom'
    };
    setCustomSymbols(prev => [...prev, newSymbol]);
  };
  
  // Remove custom symbol
  const removeCustomSymbol = (symbolId: string) => {
    setCustomSymbols(prev => prev.filter(s => s.id !== symbolId));
  };
  
  // Get position classes
  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      default:
        return 'bottom-4 left-4';
    }
  };
  
  return (
    <div className={`absolute ${getPositionClasses()} z-40`}>
      <div className="bg-gray-900 bg-opacity-95 border border-gray-600 rounded-lg shadow-lg backdrop-blur-sm min-w-[250px] max-w-[350px]">
        {/* Header */}
        <div className="flex items-center justify-between p-3 border-b border-gray-600">
          <div className="flex items-center space-x-2">
            <span className="text-xs font-mono text-gray-300 font-semibold">
              TACTICAL LEGEND
            </span>
            <span className="text-xs text-gray-500">
              ({legendItems.length} items)
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsEditing(!isEditing)}
              className="p-1 h-6 w-6 text-gray-400 hover:text-white"
              title={isEditing ? "Stop Editing" : "Edit Legend"}
            >
              {isEditing ? <X size={12} /> : <Edit3 size={12} />}
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setShowCustomSymbols(!showCustomSymbols)}
              className="p-1 h-6 w-6 text-gray-400 hover:text-white"
              title="Custom Symbols"
            >
              <Settings size={12} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="p-1 h-6 w-6 text-gray-400 hover:text-white"
            >
              {isCollapsed ? <ChevronUp size={12} /> : <ChevronDown size={12} />}
            </Button>
          </div>
        </div>
        
        {/* Content */}
        {!isCollapsed && (
          <div className="p-3 space-y-2 max-h-[400px] overflow-y-auto">
            {/* Legend items */}
            {legendItems.map(item => (
              <div
                key={item.id}
                className="flex items-center justify-between p-2 rounded bg-gray-800 bg-opacity-50"
              >
                <div className="flex items-center space-x-3 flex-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => toggleItemVisibility(item.id)}
                    className="p-1 h-6 w-6 text-gray-400 hover:text-white"
                  >
                    {item.visible ? <Eye size={12} /> : <EyeOff size={12} />}
                  </Button>
                  
                  <div
                    className="w-4 h-4 rounded flex items-center justify-center text-xs"
                    style={{ backgroundColor: item.color }}
                  >
                    {item.symbol}
                  </div>
                  
                  <div className="flex-1">
                    <div className="text-xs text-gray-300 font-medium">
                      {item.label}
                    </div>
                    <div className="text-xs text-gray-500">
                      {item.count} item{item.count !== 1 ? 's' : ''}
                    </div>
                  </div>
                </div>
                
                {isEditing && (
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => startEditing(item.id)}
                    className="p-1 h-6 w-6 text-gray-400 hover:text-white"
                  >
                    <Edit3 size={12} />
                  </Button>
                )}
              </div>
            ))}
            
            {/* Custom symbols section */}
            {showCustomSymbols && (
              <div className="border-t border-gray-600 pt-3 mt-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-xs font-mono text-gray-400">
                    CUSTOM SYMBOLS
                  </span>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={addCustomSymbol}
                    className="p-1 h-6 w-6 text-green-400 hover:text-green-300"
                    title="Add Custom Symbol"
                  >
                    <Plus size={12} />
                  </Button>
                </div>
                
                {customSymbols.map(symbol => (
                  <div
                    key={symbol.id}
                    className="flex items-center justify-between p-2 rounded bg-gray-800 bg-opacity-30"
                  >
                    <div className="flex items-center space-x-3 flex-1">
                      <div
                        className="w-4 h-4 rounded flex items-center justify-center text-xs"
                        style={{ backgroundColor: symbol.color }}
                      >
                        {symbol.symbol}
                      </div>
                      <div className="flex-1">
                        <div className="text-xs text-gray-300">
                          {symbol.name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {symbol.category}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => startEditing(symbol.id)}
                        className="p-1 h-5 w-5 text-gray-400 hover:text-white"
                      >
                        <Edit3 size={10} />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => removeCustomSymbol(symbol.id)}
                        className="p-1 h-5 w-5 text-red-400 hover:text-red-300"
                      >
                        <Trash2 size={10} />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            {/* Empty state */}
            {legendItems.length === 0 && (
              <div className="text-center py-4">
                <div className="text-gray-500 text-sm">
                  No data to display
                </div>
                <div className="text-gray-600 text-xs mt-1">
                  Legend will populate when incidents or responses are loaded
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default TacticalLegend;
