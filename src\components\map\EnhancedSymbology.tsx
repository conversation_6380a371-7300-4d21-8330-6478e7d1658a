import React, { useState, useRef, useCallback } from 'react';
import maplibregl from 'maplibre-gl';
import {
  Palette,
  Star,
  Shield,
  Target,
  Navigation,
  Zap,
  Settings,
  Save,
  Upload,
  Download,
  Plus,
  Trash2,
  Edit3
} from 'lucide-react';
import Button from '@/components/ui/Button';

interface EnhancedSymbologyProps {
  map: maplibregl.Map;
}

interface MilitarySymbol {
  id: string;
  name: string;
  category: 'friendly' | 'hostile' | 'neutral' | 'unknown';
  type: 'unit' | 'equipment' | 'installation' | 'activity';
  symbol: string;
  color: string;
  size: number;
  strokeColor: string;
  strokeWidth: number;
  customizable: boolean;
}

interface SymbolCategory {
  id: string;
  name: string;
  symbols: MilitarySymbol[];
  visible: boolean;
}

const EnhancedSymbology: React.FC<EnhancedSymbologyProps> = ({ map }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('friendly');
  const [editingSymbol, setEditingSymbol] = useState<string | null>(null);
  const [placementMode, setPlacementMode] = useState<string | null>(null);
  
  const symbolsRef = useRef<Map<string, maplibregl.Marker>>(new Map());
  
  // Default military symbols
  const [symbolCategories, setSymbolCategories] = useState<SymbolCategory[]>([
    {
      id: 'friendly',
      name: 'Friendly Forces',
      visible: true,
      symbols: [
        {
          id: 'friendly-infantry',
          name: 'Infantry Unit',
          category: 'friendly',
          type: 'unit',
          symbol: '⬜',
          color: '#0066CC',
          size: 24,
          strokeColor: '#FFFFFF',
          strokeWidth: 2,
          customizable: true
        },
        {
          id: 'friendly-armor',
          name: 'Armor Unit',
          category: 'friendly',
          type: 'unit',
          symbol: '⬜',
          color: '#0066CC',
          size: 28,
          strokeColor: '#FFFFFF',
          strokeWidth: 2,
          customizable: true
        },
        {
          id: 'friendly-artillery',
          name: 'Artillery Unit',
          category: 'friendly',
          type: 'unit',
          symbol: '⚫',
          color: '#0066CC',
          size: 20,
          strokeColor: '#FFFFFF',
          strokeWidth: 2,
          customizable: true
        },
        {
          id: 'friendly-command',
          name: 'Command Post',
          category: 'friendly',
          type: 'installation',
          symbol: '⬜',
          color: '#0066CC',
          size: 32,
          strokeColor: '#FFFFFF',
          strokeWidth: 3,
          customizable: true
        }
      ]
    },
    {
      id: 'hostile',
      name: 'Hostile Forces',
      visible: true,
      symbols: [
        {
          id: 'hostile-infantry',
          name: 'Enemy Infantry',
          category: 'hostile',
          type: 'unit',
          symbol: '◆',
          color: '#CC0000',
          size: 24,
          strokeColor: '#FFFFFF',
          strokeWidth: 2,
          customizable: true
        },
        {
          id: 'hostile-armor',
          name: 'Enemy Armor',
          category: 'hostile',
          type: 'unit',
          symbol: '◆',
          color: '#CC0000',
          size: 28,
          strokeColor: '#FFFFFF',
          strokeWidth: 2,
          customizable: true
        },
        {
          id: 'hostile-position',
          name: 'Enemy Position',
          category: 'hostile',
          type: 'installation',
          symbol: '◆',
          color: '#CC0000',
          size: 20,
          strokeColor: '#FFFFFF',
          strokeWidth: 2,
          customizable: true
        }
      ]
    },
    {
      id: 'neutral',
      name: 'Neutral/Civilian',
      visible: true,
      symbols: [
        {
          id: 'neutral-civilian',
          name: 'Civilian Area',
          category: 'neutral',
          type: 'installation',
          symbol: '◯',
          color: '#00CC00',
          size: 20,
          strokeColor: '#FFFFFF',
          strokeWidth: 2,
          customizable: true
        },
        {
          id: 'neutral-medical',
          name: 'Medical Facility',
          category: 'neutral',
          type: 'installation',
          symbol: '✚',
          color: '#CC0000',
          size: 24,
          strokeColor: '#FFFFFF',
          strokeWidth: 2,
          customizable: false
        }
      ]
    },
    {
      id: 'unknown',
      name: 'Unknown/Pending',
      visible: true,
      symbols: [
        {
          id: 'unknown-unit',
          name: 'Unknown Unit',
          category: 'unknown',
          type: 'unit',
          symbol: '?',
          color: '#FFCC00',
          size: 24,
          strokeColor: '#000000',
          strokeWidth: 2,
          customizable: true
        }
      ]
    }
  ]);
  
  // Create symbol element
  const createSymbolElement = useCallback((symbol: MilitarySymbol): HTMLElement => {
    const element = document.createElement('div');
    element.className = 'military-symbol';
    element.style.cssText = `
      width: ${symbol.size}px;
      height: ${symbol.size}px;
      background: ${symbol.color};
      border: ${symbol.strokeWidth}px solid ${symbol.strokeColor};
      border-radius: ${symbol.type === 'unit' ? '0' : '50%'};
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: ${symbol.size * 0.6}px;
      font-weight: bold;
      color: ${symbol.strokeColor};
      cursor: pointer;
      user-select: none;
      box-shadow: 0 2px 4px rgba(0,0,0,0.3);
    `;
    
    element.innerHTML = symbol.symbol;
    
    return element;
  }, []);
  
  // Handle map click for symbol placement
  const handleMapClick = useCallback((e: maplibregl.MapMouseEvent) => {
    if (!placementMode) return;
    
    const symbol = symbolCategories
      .flatMap(cat => cat.symbols)
      .find(s => s.id === placementMode);
    
    if (!symbol) return;
    
    const { lng, lat } = e.lngLat;
    const symbolId = `placed-${symbol.id}-${Date.now()}`;
    
    // Create marker
    const element = createSymbolElement(symbol);
    const marker = new maplibregl.Marker({ element })
      .setLngLat([lng, lat])
      .addTo(map);
    
    // Add popup with symbol info
    const popup = new maplibregl.Popup({
      closeButton: true,
      closeOnClick: false
    })
      .setHTML(`
        <div class="tactical-popup">
          <h3 class="text-sm font-bold text-white mb-2">${symbol.name}</h3>
          <div class="text-xs text-gray-300">
            <div><strong>Category:</strong> ${symbol.category}</div>
            <div><strong>Type:</strong> ${symbol.type}</div>
            <div><strong>Position:</strong> ${lat.toFixed(6)}, ${lng.toFixed(6)}</div>
          </div>
          <button onclick="this.closest('.maplibregl-popup').remove(); this.closest('.maplibregl-marker').remove();" 
                  class="mt-2 px-2 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700">
            Remove Symbol
          </button>
        </div>
      `);
    
    // Add click handler to show popup
    element.addEventListener('click', (e) => {
      e.stopPropagation();
      popup.setLngLat([lng, lat]).addTo(map);
    });
    
    symbolsRef.current.set(symbolId, marker);
    
  }, [placementMode, symbolCategories, createSymbolElement, map]);
  
  // Toggle symbol placement mode
  const togglePlacementMode = (symbolId: string) => {
    if (placementMode === symbolId) {
      setPlacementMode(null);
      map.getCanvas().style.cursor = '';
    } else {
      setPlacementMode(symbolId);
      map.getCanvas().style.cursor = 'crosshair';
    }
  };
  
  // Add custom symbol
  const addCustomSymbol = () => {
    const newSymbol: MilitarySymbol = {
      id: `custom-${Date.now()}`,
      name: 'Custom Symbol',
      category: selectedCategory as any,
      type: 'unit',
      symbol: '★',
      color: '#FFFFFF',
      size: 24,
      strokeColor: '#000000',
      strokeWidth: 2,
      customizable: true
    };
    
    setSymbolCategories(prev => prev.map(cat => 
      cat.id === selectedCategory 
        ? { ...cat, symbols: [...cat.symbols, newSymbol] }
        : cat
    ));
  };
  
  // Remove symbol
  const removeSymbol = (symbolId: string) => {
    setSymbolCategories(prev => prev.map(cat => ({
      ...cat,
      symbols: cat.symbols.filter(s => s.id !== symbolId)
    })));
  };
  
  // Export symbols
  const exportSymbols = () => {
    const data = {
      categories: symbolCategories,
      timestamp: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `military-symbols-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };
  
  // Import symbols
  const importSymbols = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string);
        if (data.categories) {
          setSymbolCategories(data.categories);
        }
      } catch (error) {
        console.error('Error importing symbols:', error);
      }
    };
    reader.readAsText(file);
  };
  
  // Set up map event listeners
  React.useEffect(() => {
    if (placementMode) {
      map.on('click', handleMapClick);
    } else {
      map.off('click', handleMapClick);
    }
    
    return () => {
      map.off('click', handleMapClick);
    };
  }, [placementMode, handleMapClick, map]);
  
  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      symbolsRef.current.forEach(marker => marker.remove());
      symbolsRef.current.clear();
    };
  }, []);
  
  if (!isVisible) {
    return (
      <Button
        size="sm"
        variant="ghost"
        onClick={() => setIsVisible(true)}
        className="absolute top-32 right-4 z-40 bg-gray-900 bg-opacity-95 border border-gray-600"
        title="Enhanced Symbology"
      >
        <Palette size={16} />
      </Button>
    );
  }
  
  const selectedCategoryData = symbolCategories.find(cat => cat.id === selectedCategory);
  
  return (
    <div className="absolute top-32 right-4 z-40 w-80">
      <div className="bg-gray-900 bg-opacity-95 border border-gray-600 rounded-lg shadow-lg backdrop-blur-sm">
        {/* Header */}
        <div className="flex items-center justify-between p-3 border-b border-gray-600">
          <div className="flex items-center space-x-2">
            <Palette size={16} className="text-blue-400" />
            <span className="text-xs font-mono text-gray-300 font-semibold">
              ENHANCED SYMBOLOGY
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <input
              type="file"
              accept=".json"
              onChange={importSymbols}
              className="hidden"
              id="import-symbols"
            />
            <Button
              size="sm"
              variant="ghost"
              onClick={() => document.getElementById('import-symbols')?.click()}
              className="p-1 h-6 w-6 text-gray-400 hover:text-white"
              title="Import Symbols"
            >
              <Upload size={12} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={exportSymbols}
              className="p-1 h-6 w-6 text-gray-400 hover:text-white"
              title="Export Symbols"
            >
              <Download size={12} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsVisible(false)}
              className="p-1 h-6 w-6 text-gray-400 hover:text-white"
            >
              ✕
            </Button>
          </div>
        </div>
        
        {/* Category tabs */}
        <div className="flex border-b border-gray-600">
          {symbolCategories.map(category => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`
                flex-1 px-2 py-2 text-xs font-medium transition-colors
                ${selectedCategory === category.id 
                  ? 'bg-blue-600 text-white' 
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }
              `}
            >
              {category.name}
            </button>
          ))}
        </div>
        
        {/* Symbol grid */}
        <div className="p-3 max-h-96 overflow-y-auto">
          <div className="flex items-center justify-between mb-3">
            <span className="text-xs text-gray-400">
              {selectedCategoryData?.symbols.length || 0} symbols
            </span>
            <Button
              size="sm"
              variant="ghost"
              onClick={addCustomSymbol}
              className="text-green-400 hover:text-green-300"
            >
              <Plus size={12} />
              <span className="ml-1">Add</span>
            </Button>
          </div>
          
          <div className="grid grid-cols-4 gap-2">
            {selectedCategoryData?.symbols.map(symbol => (
              <div
                key={symbol.id}
                className={`
                  relative p-2 border rounded cursor-pointer transition-all
                  ${placementMode === symbol.id 
                    ? 'border-blue-400 bg-blue-900 bg-opacity-50' 
                    : 'border-gray-600 hover:border-gray-500'
                  }
                `}
                onClick={() => togglePlacementMode(symbol.id)}
                title={symbol.name}
              >
                <div className="flex flex-col items-center space-y-1">
                  <div
                    className="w-6 h-6 flex items-center justify-center text-sm font-bold"
                    style={{
                      backgroundColor: symbol.color,
                      color: symbol.strokeColor,
                      border: `1px solid ${symbol.strokeColor}`,
                      borderRadius: symbol.type === 'unit' ? '0' : '50%'
                    }}
                  >
                    {symbol.symbol}
                  </div>
                  <span className="text-xs text-gray-300 text-center leading-tight">
                    {symbol.name}
                  </span>
                </div>
                
                {symbol.customizable && (
                  <div className="absolute top-1 right-1 flex space-x-1">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setEditingSymbol(symbol.id);
                      }}
                      className="text-gray-400 hover:text-white"
                    >
                      <Edit3 size={8} />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        removeSymbol(symbol.id);
                      }}
                      className="text-red-400 hover:text-red-300"
                    >
                      <Trash2 size={8} />
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
          
          {placementMode && (
            <div className="mt-3 p-2 bg-blue-900 bg-opacity-30 border border-blue-600 rounded text-xs text-blue-300">
              Click on the map to place the selected symbol. Click here to cancel.
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedSymbology;
