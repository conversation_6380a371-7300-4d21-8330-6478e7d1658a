import { IncidentType, ActionType } from '@/types/incident';

// Symbol configuration interface
export interface SymbolConfig {
  symbol: string;
  color: string;
  description: string;
  shape?: string;
  text?: string;
}

// Tactical symbols for incidents
export const tacticalSymbols: Record<string, SymbolConfig> = {
  physical_raid: {
    symbol: '⚔️',
    color: '#FF4444',
    description: 'Physical Raid',
    shape: 'diamond',
    text: 'PR'
  },
  cyber_attack: {
    symbol: '💻',
    color: '#4444FF',
    description: 'Cyber Attack',
    shape: 'square',
    text: 'CY'
  },
  intelligence_gathering: {
    symbol: '🔍',
    color: '#44FF44',
    description: 'Intelligence Gathering',
    shape: 'circle',
    text: 'IG'
  },
  sabotage: {
    symbol: '💥',
    color: '#FF8844',
    description: 'Sabotage',
    shape: 'triangle',
    text: 'SAB'
  },
  assassination: {
    symbol: '🎯',
    color: '#FF44FF',
    description: 'Assassination',
    shape: 'star',
    text: 'ASS'
  },
  kidnapping: {
    symbol: '👤',
    color: '#FFFF44',
    description: 'Kidnapping',
    shape: 'hexagon',
    text: 'KID'
  },
  bombing: {
    symbol: '💣',
    color: '#FF0000',
    description: 'Bombing',
    shape: 'diamond',
    text: 'BOM'
  },
  other: {
    symbol: '❓',
    color: '#888888',
    description: 'Other Incident',
    shape: 'circle',
    text: 'OTH'
  }
};

// Response symbols for different action types
export const responseSymbols: Record<string, SymbolConfig> = {
  surveillance: {
    symbol: '👁️',
    color: '#00AA00',
    description: 'Surveillance Operation',
    shape: 'circle',
    text: 'SUR'
  },
  raid: {
    symbol: '🏠',
    color: '#0066CC',
    description: 'Raid Operation',
    shape: 'square',
    text: 'RAID'
  },
  arrest: {
    symbol: '🚔',
    color: '#FF6600',
    description: 'Arrest Operation',
    shape: 'diamond',
    text: 'ARR'
  },
  investigation: {
    symbol: '🔍',
    color: '#9966CC',
    description: 'Investigation',
    shape: 'hexagon',
    text: 'INV'
  },
  patrol: {
    symbol: '🚶',
    color: '#00CCCC',
    description: 'Patrol Operation',
    shape: 'triangle',
    text: 'PAT'
  },
  checkpoint: {
    symbol: '🛑',
    color: '#CC6600',
    description: 'Checkpoint',
    shape: 'square',
    text: 'CP'
  },
  escort: {
    symbol: '🚗',
    color: '#6600CC',
    description: 'Escort Operation',
    shape: 'circle',
    text: 'ESC'
  },
  search: {
    symbol: '🔎',
    color: '#CC0066',
    description: 'Search Operation',
    shape: 'star',
    text: 'SRC'
  },
  other: {
    symbol: '⚙️',
    color: '#666666',
    description: 'Other Response',
    shape: 'circle',
    text: 'OTH'
  }
};

// Function to get incident symbol configuration
export const getIncidentSymbol = (type: IncidentType): SymbolConfig => {
  return tacticalSymbols[type] || tacticalSymbols.other;
};

// Function to get response symbol configuration
export const getResponseSymbol = (actionType: ActionType): SymbolConfig => {
  return responseSymbols[actionType] || responseSymbols.other;
};

// Function to create tactical icon (for backward compatibility)
export const createTacticalIcon = (type: IncidentType, size: number = 32): string => {
  const config = getIncidentSymbol(type);
  
  // Generate SVG path based on shape
  let svgPath = '';
  const shape = config.shape || 'circle';
  
  switch (shape) {
    case 'diamond':
      svgPath = '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" />';
      break;
    case 'square':
      svgPath = '<rect x="4" y="4" width="24" height="24" fill-opacity="0.9" stroke="#000" stroke-width="1" />';
      break;
    case 'triangle':
      svgPath = '<polygon points="16,2 30,30 2,30" fill-opacity="0.9" stroke="#000" stroke-width="1" />';
      break;
    case 'hexagon':
      svgPath = '<polygon points="16,2 2,12 2,22 16,30 30,22 30,12" fill-opacity="0.9" stroke="#000" stroke-width="1" />';
      break;
    case 'star':
      svgPath = '<polygon points="16,2 19,12 30,12 21,18 25,28 16,22 7,28 11,18 2,12 13,12" fill-opacity="0.9" stroke="#000" stroke-width="1" />';
      break;
    case 'circle':
    default:
      svgPath = '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" />';
      break;
  }
  
  // Add text if specified
  if (config.text) {
    svgPath += `<text x="16" y="20" font-size="8" text-anchor="middle" fill="#fff" font-weight="bold">${config.text}</text>`;
  }
  
  return `
    <svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 32 32">
      <g fill="${config.color}">
        ${svgPath}
      </g>
    </svg>
  `;
};

// Function to create response icon (for backward compatibility)
export const createResponseIcon = (actionType: ActionType, size: number = 28): string => {
  const config = getResponseSymbol(actionType);
  
  // Generate SVG path based on shape
  let svgPath = '';
  const shape = config.shape || 'circle';
  
  switch (shape) {
    case 'diamond':
      svgPath = '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" />';
      break;
    case 'square':
      svgPath = '<rect x="4" y="4" width="24" height="24" fill-opacity="0.9" stroke="#000" stroke-width="1" />';
      break;
    case 'triangle':
      svgPath = '<polygon points="16,2 30,30 2,30" fill-opacity="0.9" stroke="#000" stroke-width="1" />';
      break;
    case 'hexagon':
      svgPath = '<polygon points="16,2 2,12 2,22 16,30 30,22 30,12" fill-opacity="0.9" stroke="#000" stroke-width="1" />';
      break;
    case 'star':
      svgPath = '<polygon points="16,2 19,12 30,12 21,18 25,28 16,22 7,28 11,18 2,12 13,12" fill-opacity="0.9" stroke="#000" stroke-width="1" />';
      break;
    case 'circle':
    default:
      svgPath = '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" />';
      break;
  }
  
  // Add text if specified
  if (config.text) {
    svgPath += `<text x="16" y="20" font-size="7" text-anchor="middle" fill="#fff" font-weight="bold">${config.text}</text>`;
  }
  
  return `
    <svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 32 32">
      <g fill="${config.color}">
        ${svgPath}
      </g>
    </svg>
  `;
};

// Military symbol categories for enhanced symbology
export const militarySymbolCategories = {
  friendly: {
    name: 'Friendly Forces',
    color: '#0066CC',
    shape: 'square'
  },
  hostile: {
    name: 'Hostile Forces', 
    color: '#CC0000',
    shape: 'diamond'
  },
  neutral: {
    name: 'Neutral/Civilian',
    color: '#00CC00',
    shape: 'circle'
  },
  unknown: {
    name: 'Unknown/Pending',
    color: '#FFCC00',
    shape: 'circle'
  }
};

// Enhanced military symbols for tactical operations
export const enhancedMilitarySymbols = {
  // Command and Control
  command_post: {
    symbol: '⌘',
    color: '#FFD700',
    description: 'Command Post',
    shape: 'square',
    text: 'CP'
  },
  observation_post: {
    symbol: '👁',
    color: '#00FF00',
    description: 'Observation Post',
    shape: 'circle',
    text: 'OP'
  },
  
  // Logistics
  supply_depot: {
    symbol: '📦',
    color: '#8B4513',
    description: 'Supply Depot',
    shape: 'square',
    text: 'SUP'
  },
  fuel_depot: {
    symbol: '⛽',
    color: '#FF8C00',
    description: 'Fuel Depot',
    shape: 'square',
    text: 'FUEL'
  },
  
  // Medical
  medical_station: {
    symbol: '🏥',
    color: '#FF0000',
    description: 'Medical Station',
    shape: 'circle',
    text: 'MED'
  },
  casualty_collection: {
    symbol: '🚑',
    color: '#FF6B6B',
    description: 'Casualty Collection Point',
    shape: 'circle',
    text: 'CCP'
  },
  
  // Communications
  radio_relay: {
    symbol: '📡',
    color: '#4169E1',
    description: 'Radio Relay',
    shape: 'triangle',
    text: 'RR'
  },
  communication_hub: {
    symbol: '📻',
    color: '#6495ED',
    description: 'Communication Hub',
    shape: 'hexagon',
    text: 'COMM'
  }
};

// Export all symbols as a combined collection
export const allTacticalSymbols = {
  incidents: tacticalSymbols,
  responses: responseSymbols,
  military: enhancedMilitarySymbols
};

export default {
  tacticalSymbols,
  responseSymbols,
  getIncidentSymbol,
  getResponseSymbol,
  createTacticalIcon,
  createResponseIcon,
  militarySymbolCategories,
  enhancedMilitarySymbols,
  allTacticalSymbols
};
